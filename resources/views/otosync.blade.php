<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Otosync - Backend Service</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        :root {
            --background-color: #f6f8fa;
            --text-color: #24292e;
            --primary-color: #0052cc;
            --border-color: #e1e4e8;
            --card-background: #ffffff;
            --card-shadow: rgba(149, 157, 165, 0.1);
        }
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
            background-color: var(--background-color);
            color: var(--text-color);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 1rem;
        }
        .main-container {
            width: 100%;
            max-width: 600px;
            background-color: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 3rem;
            box-shadow: 0 8px 24px var(--card-shadow);
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .main-container:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 28px var(--card-shadow);
        }
        .header {
            margin-bottom: 2rem;
        }
        .logo {
            color: var(--primary-color);
            font-size: 3.5rem;
            font-weight: 700;
            letter-spacing: -1px;
            margin-bottom: 0.5rem;
        }
        .tagline {
            font-size: 1.25rem;
            font-weight: 400;
            color: #586069;
        }
        .content {
            border-top: 1px solid var(--border-color);
            padding-top: 2rem;
        }
        .status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            padding: 0.75rem 1.25rem;
            background-color: #f1f8ff;
            border: 1px solid #c8e1ff;
            border-radius: 6px;
            color: #0366d6;
            font-weight: 500;
            margin-bottom: 1.5rem;
        }
        .status .fas {
            color: #28a745;
        }
        .version-info {
            font-size: 0.9rem;
            color: #6a737d;
        }
        .version-info .version-tag {
            font-weight: 600;
            color: var(--primary-color);
            background-color: #e6f0ff;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
        }
        .footer {
            margin-top: 2.5rem;
            font-size: 0.85rem;
            color: #959da5;
        }
        @media (max-width: 640px) {
            .main-container {
                padding: 2rem;
            }
            .logo {
                font-size: 2.8rem;
            }
            .tagline {
                font-size: 1.1rem;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <header class="header">
            <h1 class="logo">OTOSYNC</h1>
            <p class="tagline">Backend Service by Zafrada</p>
        </header>
        <main class="content">
            <div class="status">
                <i class="fas fa-check-circle"></i>
                <span>All Systems Operational</span>
            </div>
            <div class="version-info">
                Current Version: <span class="version-tag">{{ config('zafrada.version', '1.0.0') }}</span>
            </div>
        </main>
        <footer class="footer">
            &copy; {{ date('Y') }} Otosync | Zafrada. All rights reserved.
        </footer>
    </div>
</body>
</html>
