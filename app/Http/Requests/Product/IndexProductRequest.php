<?php

namespace App\Http\Requests\Product;

use Illuminate\Foundation\Http\FormRequest;

/**
 * @method $this merge(array $input)
 * @method $this mergeIfMissing(array $input)
 */
class IndexProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'page' => 'required|integer|min:1',
            'per_page' => 'required|integer|min:1|max:200',
            'search' => 'nullable|string|max:255',
            'brand' => 'nullable|string|max:255',
            'sort_by' => 'sometimes|string|in:created_at,updated_at,name,brand_name',
            'sort_direction' => 'sometimes|string|in:asc,desc',
            'has_stock' => 'sometimes|boolean',

            // New filters
            'min_price' => 'nullable|numeric|min:0',
            'max_price' => 'nullable|numeric|min:0',
            'is_consignment' => 'nullable|boolean',
            'attributes' => 'nullable|string',
            'attribute_pairs' => 'nullable|array',
            'attribute_pairs.*.attribute' => 'required_with:attribute_pairs|string',
            'attribute_pairs.*.value' => 'required_with:attribute_pairs|string',

            // New validation for multiple family/ukuran pairs
            'attribute_sets' => 'nullable|array',
            'attribute_sets.*.family' => 'required_with:attribute_sets|string',
            'attribute_sets.*.ukuran' => 'required_with:attribute_sets|string',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'page.required' => 'Page number is required',
            'page.integer' => 'Page must be an integer',
            'page.min' => 'Page must be at least 1',
            'per_page.required' => 'Per page value is required',
            'per_page.integer' => 'Per page must be an integer',
            'per_page.min' => 'Per page must be at least 1',
            'per_page.max' => 'Per page cannot exceed 200',
            'sort_by.in' => 'Invalid sort by value. Allowed values are created_at, updated_at, name, brand_name',
            'sort_direction.in' => 'Invalid sort direction. Allowed values are asc, desc',
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        $this->merge([
            'sort_by' => $this->sort_by ?? 'created_at',
            'sort_direction' => $this->sort_direction ?? 'desc',
        ]);
    }
}
