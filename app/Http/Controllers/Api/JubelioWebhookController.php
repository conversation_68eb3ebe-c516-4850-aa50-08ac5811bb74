<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Jobs\ProcessJubelioStockUpdate;
use App\Jobs\SyncJubelioProductFromWebhook;
use App\Models\SyncLog;
use App\Services\Jubelio\WebhookService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class JubelioWebhookController extends Controller
{
    protected $webhookService;

    public function __construct(WebhookService $webhookService)
    {
        $this->webhookService = $webhookService;
    }

    /**
     * Handle product data from Jubelio webhook.
     */
    public function handleProduct(Request $request)
    {
        if (!$this->webhookService->verifyWebhookSignature($request)) {
            return response()->json(['status' => 'rejected', 'error' => 'Invalid signature.'], 403);
        }

        $payload = $request->all();
        Log::info('Jubelio product webhook received:', [
            'payload' => $payload,
        ]);

        $itemGroupId = $payload['item_group_id'] ?? null;

        if ($itemGroupId) {
            SyncJubelioProductFromWebhook::dispatch($itemGroupId, null, SyncLog::TYPE_WEBHOOK);
            return response()->json(['status' => 'ok', 'message' => 'Product data received and queued for processing.']);
        }

        return response()->json(['status' => 'rejected', 'error' => 'Missing item_group_id.'], 400);
    }

    /**
     * Handle stock data from Jubelio webhook.
     */
    public function handleStock(Request $request)
    {
        if (!$this->webhookService->verifyWebhookSignature($request)) {
            return response()->json(['status' => 'rejected', 'error' => 'Invalid signature.'], 403);
        }

        Log::info('Jubelio stock webhook received:', [
            'payload' => $request->all(),
        ]);

        $itemIds = $request->input('item_ids');
        Log::info('Item IDs received:', $itemIds);

        if (is_array($itemIds) && count($itemIds) > 0) {
            ProcessJubelioStockUpdate::dispatch($itemIds);
        }

        return response()->json(['status' => 'ok', 'message' => 'Stock data received and queued for processing.']);
    }

    /**
     * Handle price data from Jubelio webhook.
     */
    public function handlePrice(Request $request)
    {
        if (!$this->webhookService->verifyWebhookSignature($request)) {
            return response()->json(['status' => 'rejected', 'error' => 'Invalid signature.'], 403);
        }

        Log::info('Jubelio price webhook received:', [
            'payload' => $request->all(),
        ]);

        // TODO: Add logic to process price data

        return response()->json(['status' => 'ok', 'message' => 'Price data received successfully.']);
    }

    /**
     * Handle stock transfer data from Jubelio webhook.
     */
    public function handleStockTransfer(Request $request)
    {
        if (!$this->webhookService->verifyWebhookSignature($request)) {
            return response()->json(['status' => 'rejected', 'error' => 'Invalid signature.'], 403);
        }

        Log::info('Jubelio stock transfer webhook received:', [
            'payload' => $request->all(),
        ]);

        // TODO: Add logic to process stock transfer data

        return response()->json(['status' => 'ok', 'message' => 'Stock transfer data received successfully.']);
    }
}
