<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use <PERSON><PERSON>\Sanctum\PersonalAccessToken;

class AuthController extends Controller
{
    public function login(LoginRequest $request)
    {
        if (! Auth::attempt($request->validated())) {
            return response()->json(['message' => 'The email or password you entered is incorrect. Please try again.'], 401);
        }

        /** @var User $user */
        $user = Auth::user();
        $tokenName = 'auth-token-' . $user->id;
        $token = $user->createToken($tokenName, ['*'], now()->addHours(12))->plainTextToken;

        return response()->json([
            'token_type' => 'Bearer',
            'token' => $token,
            'message' => 'Login successfully',
        ], 200);
    }

    public function logout(Request $request)
    {
        /** @var PersonalAccessToken|null $token */
        $token = $request->user()->currentAccessToken();

        if ($token) {
            $token->delete();
        }

        return response()->json(['message' => 'Logged out successfully'], 200);
    }
}
