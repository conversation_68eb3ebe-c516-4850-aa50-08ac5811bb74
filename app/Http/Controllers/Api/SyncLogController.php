<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\SyncLog\IndexSyncLogRequest;
use App\Http\Resources\SyncLog\SyncLogDetailResource;
use App\Http\Resources\SyncLog\SyncLogListResource;
use App\Models\SyncLog;
use Illuminate\Support\Facades\Log;

class SyncLogController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(IndexSyncLogRequest $request)
    {
        $validated = $request->validated();
        // Log::info('SyncLogController@index', $validated);

        $query = SyncLog::query();

        if ($level = $validated['level'] ?? null) {
            $query->where('level', $level);
        }

        if ($syncType = $validated['sync_type'] ?? null) {
            $query->where('sync_type', $syncType);
        }

        if ($search = $validated['search'] ?? null) {
            $query->where('message', 'like', "%{$search}%");
        }

        $query->orderBy($validated['sort_by'], $validated['sort_direction']);

        $logs = $query->paginate(
            perPage: $validated['per_page'],
            page: $validated['page']
        );

        return SyncLogListResource::collection($logs);
    }

    /**
     * Display the specified resource.
     */
    public function show(SyncLog $syncLog)
    {
        return new SyncLogDetailResource($syncLog);
    }
}
