<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Jobs\StartFullProductSync;
use App\Jobs\SyncJubelioProductFromWebhook;
use App\Jobs\SyncJubelioProductVariant;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\SyncLog;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ProductSyncController extends Controller
{
    /**
     * Dispatch jobs to sync all products from Jubelio.
     */
    public function syncAll(): JsonResponse
    {
        StartFullProductSync::dispatch(SyncLog::TYPE_MANUAL_FULL);

        return response()->json([
            'status' => 'success',
            'message' => 'Jubelio product sync has been initiated.'
        ], 200);
    }

    /**
     * Dispatch a job to sync a single product from Jubelio.
     */
    public function syncSingle(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'item_group_id' => 'required|integer',
            'thumbnail' => 'nullable|string|url',
        ]);

        SyncJubelioProductFromWebhook::dispatch(
            $validated['item_group_id'],
            $validated['thumbnail'] ?? null,
            SyncLog::TYPE_MANUAL_SINGLE
        );

        return response()->json([
            'status' => 'success',
            'message' => 'Single product sync has been initiated.'
        ], 200);
    }

    /**
     * Check the sync status for a batch of Jubelio products.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkSyncStatusBatch(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'jubelio_item_group_ids' => 'required|array',
            'jubelio_item_group_ids.*' => 'integer',
        ]);

        $itemGroupIds = $validated['jubelio_item_group_ids'];

        // Find all products that match the given item group IDs
        $syncedProducts = Product::whereIn('jubelio_item_group_id', $itemGroupIds)
            ->pluck('jubelio_item_group_id')
            ->all();

        // Determine which IDs were not found (not synced)
        $notSyncedIds = array_diff($itemGroupIds, $syncedProducts);

        return response()->json([
            'status' => 'success',
            'data' => [
                'synced' => $syncedProducts,
                'not_synced' => array_values($notSyncedIds), // Reset array keys
            ]
        ]);
    }
    /**
     * Dispatch a job to sync a single product variant from Jubelio.
     */
    public function syncSingleVariant(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'jubelio_item_id' => 'required|integer',
        ]);

        SyncJubelioProductVariant::dispatch($validated['jubelio_item_id']);

        return response()->json([
            'status' => 'success',
            'message' => 'Single product variant sync has been initiated.'
        ], 200);
    }

    /**
     * Check the sync status for a batch of Jubelio product variants.
     */
    public function checkVariantSyncStatusBatch(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'jubelio_item_ids' => 'required|array',
            'jubelio_item_ids.*' => 'integer',
        ]);

        $itemIds = $validated['jubelio_item_ids'];

        // Find all product variants that match the given item IDs
        $syncedVariants = ProductVariant::whereIn('jubelio_item_id', $itemIds)
            ->pluck('jubelio_item_id')
            ->all();

        // Determine which IDs were not found (not synced)
        $notSyncedIds = array_diff($itemIds, $syncedVariants);

        return response()->json([
            'status' => 'success',
            'data' => [
                'synced' => $syncedVariants,
                'not_synced' => array_values($notSyncedIds), // Reset array keys
            ]
        ]);
    }
}
