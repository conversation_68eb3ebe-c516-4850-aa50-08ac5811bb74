<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Product\IndexProductRequest;
use App\Http\Resources\Product\ProductDetailResource;
use App\Http\Resources\Product\ProductListResource;
use App\Models\Product;
use Illuminate\Database\Eloquent\Builder;
use Log;

class ProductController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(IndexProductRequest $request)
    {
        $validated = $request->validated();
        // Log::info('validated', $validated);

        $query = Product::with('variants');

        // Search functionality
        if (!empty($validated['search'])) {
            $query->where(function ($q) use ($validated) {
                $q->where('name', 'like', "%{$validated['search']}%")
                    ->orWhere('brand_name', 'like', "%{$validated['search']}%")
                    ->orWhere('jubelio_item_group_id', 'like', "%{$validated['search']}%");
            });
        }

        // Filter by brand
        if (!empty($validated['brand'])) {
            $query->where('brand_name', $validated['brand']);
        }

        // Filter by stock
        if (!empty($validated['has_stock'])) {
            $query->whereHas('variants', function ($q) {
                $q->where('available_qty', '>', 0);
            });
        }

        // Filter by price range
        if (!empty($validated['min_price'])) {
            $query->whereHas('variants', function ($q) use ($validated) {
                $q->where('sell_price', '>=', $validated['min_price']);
            });
        }

        if (!empty($validated['max_price'])) {
            $query->whereHas('variants', function ($q) use ($validated) {
                $q->where('sell_price', '<=', $validated['max_price']);
            });
        }

        // Filter by consignment
        if (isset($validated['is_consignment'])) {
            $query->where('is_consignment', $validated['is_consignment']);
        }

        // Filter by attributes
        if (!empty($validated['attributes'])) {
            $query->whereHas('variants.attributeValues', function ($q) use ($validated) {
                $q->where('value', 'like', '%' . $validated['attributes'] . '%');
            });
        }

        // Filter by attribute pairs
        if (!empty($validated['attribute_pairs'])) {
            $query->where(function ($q) use ($validated) {
                foreach ($validated['attribute_pairs'] as $pair) {
                    $q->orWhereHas('variants', function ($q2) use ($pair) {
                        $q2->where('available_qty', '>', 0)
                            ->whereHas('attributeValues', function ($q3) use ($pair) {
                                $q3->where('value', 'like', '%' . $pair['value'] . '%')
                                    ->whereHas('attribute', function ($q4) use ($pair) {
                                        $q4->where('name', $pair['attribute']);
                                    });
                            });
                    });
                }
            });
        }

        // New filter logic for multiple attribute sets
        if (!empty($validated['attribute_sets'])) {
            $query->where(function (Builder $q) use ($validated) {
                foreach ($validated['attribute_sets'] as $set) {
                    $q->whereHas('variants', function (Builder $q2) use ($set) {
                        $q2->where('available_qty', '>', 0);
                        // Condition for Family
                        $q2->whereHas('attributeValues', function (Builder $q3) use ($set) {
                            $q3->where('value', 'like', '%' . $set['family'] . '%')
                                ->whereHas('attribute', function (Builder $q4) {
                                    $q4->where('name', 'FAMILY');
                                });
                        });
                        // Condition for Ukuran
                        $q2->whereHas('attributeValues', function (Builder $q3) use ($set) {
                            $q3->where('value', 'like', '%' . $set['ukuran'] . '%')
                                ->whereHas('attribute', function (Builder $q4) {
                                    $q4->where('name', 'Ukuran');
                                });
                        });
                    });
                }
            });
        }

        // Apply sorting
        $query->orderBy($validated['sort_by'], $validated['sort_direction']);

        $products = $query->distinct()->paginate(
            perPage: $validated['per_page'],
            page: $validated['page']
        );

        return ProductListResource::collection($products);
    }

    /**
     * Display the specified resource.
     */
    public function show(Product $product)
    {
        $product->load('variants.attributeValues.attribute', 'variants.images', 'images');

        return new ProductDetailResource($product);
    }
}
