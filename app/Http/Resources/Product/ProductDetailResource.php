<?php

namespace App\Http\Resources\Product;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\Product\ProductVariantResource;
use App\Http\Resources\Product\ImageResource;

/**
 * @mixin \App\Models\Product
 */
class ProductDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'brand_name' => $this->brand_name,
            // 'thumbnail_path' => $this->thumbnail_path ? config('app.url').'/storage/'.$this->thumbnail_path : null,
            'thumbnail_path' => $this->thumbnail_path ? config('otosync.image_base_url') . $this->thumbnail_path : null,
            'total_stock' => $this->whenLoaded('variants', function () {
                return $this->variants->sum('available_qty');
            }),
            'variants' => ProductVariantResource::collection($this->whenLoaded('variants')),
            'images' => ImageResource::collection($this->whenLoaded('images')),
        ];
    }
}
