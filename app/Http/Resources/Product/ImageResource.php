<?php

namespace App\Http\Resources\Product;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\Image
 */
class ImageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            // 'id' => $this->id,
            // 'path' => $this->path ? config('app.url').'/storage/'.$this->path : null,
            'original_url' => $this->original_url,
            'path' => $this->path ? config('otosync.image_base_url') . $this->path : null,
            'original_image_url' => $this->original_image_url,
            'original_image_path' => $this->original_image_path ? config('otosync.image_base_url') . $this->original_image_path : null,
        ];
    }
}
