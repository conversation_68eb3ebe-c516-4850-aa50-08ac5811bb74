<?php

namespace App\Http\Resources\Product;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\Product\AttributeValueResource;
use App\Http\Resources\Product\ImageResource;

/**
 * @mixin \App\Models\ProductVariant
 */
class ProductVariantResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            // 'sku' => $this->sku,
            'name' => $this->name,
            'sell_price' => $this->sell_price,
            'weight' => $this->weight,
            'unit' => $this->unit,
            // 'barcode' => $this->barcode,
            'available_qty' => $this->available_qty,
            'on_hand_qty' => $this->on_hand_qty,
            'on_order_qty' => $this->on_order_qty,
            'attribute_values' => AttributeValueResource::collection($this->whenLoaded('attributeValues')),
            'images' => ImageResource::collection($this->whenLoaded('images')),
        ];
    }
}
