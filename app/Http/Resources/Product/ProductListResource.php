<?php

namespace App\Http\Resources\Product;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\Product
 */
class ProductListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'brand_name' => $this->brand_name,
            // 'thumbnail_original_url' => $this->thumbnail_original_url,
            // 'thumbnail_path' => $this->thumbnail_path ? config('app.url').'/storage/'.$this->thumbnail_path : null,
            'thumbnail_path' => $this->thumbnail_path ? config('otosync.image_base_url') . $this->thumbnail_path : null,
            'sell_price' => $this->price_data,
            'total_stock' => $this->whenLoaded('variants', function () {
                return $this->variants->sum('available_qty');
            }),
        ];
    }
}
