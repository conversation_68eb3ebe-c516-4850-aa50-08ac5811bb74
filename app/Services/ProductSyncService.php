<?php

namespace App\Services;

use App\Jobs\DeleteImageRecordAndFile;
use App\Jobs\DownloadImage;
use App\Jobs\SyncJubelioProduct;
use App\Models\Attribute;
use App\Models\AttributeValue;
use App\Models\Image;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Services\Jubelio\ProductService as JubelioProductService;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Throwable;

class ProductSyncService
{
    public function __construct(protected JubelioProductService $jubelioProductService)
    {
    }

    public function syncProductFromJubelio(array $productSummary, array $productDetail): void
    {
        $this->syncProduct($productSummary, $productDetail);
    }

    public function syncProductFromJubelioDetail(array $productDetail, ?string $thumbnail = null): void
    {
        // Create a synthetic summary from the detail data
        $itemIds = collect($productDetail['product_skus'])->pluck('item_id')->all();
        $stocks = $this->jubelioProductService->getProductAllStock($itemIds);
        $stockMap = collect($stocks['data'] ?? [])->keyBy('item_id');

        $productSummary = [
            'item_group_id' => $productDetail['item_group_id'],
            'is_consignment' => false,
            'variants' => collect($productDetail['product_skus'])->map(function ($sku) use ($stockMap) {
                $stock = $stockMap->get($sku['item_id']);
                return [
                    'item_id' => $sku['item_id'],
                    'item_name' => $stock['item_name'] ?? null,
                    'available_qty' => $stock['total_stocks']['available'] ?? 0,
                    'end_qty' => $stock['total_stocks']['on_hand'] ?? 0,
                    'order_qty' => $stock['total_stocks']['on_order'] ?? 0,
                ];
            })->all(),
        ];

        if ($thumbnail) {
            $productSummary['thumbnail'] = $thumbnail;
        }

        $this->syncProduct($productSummary, $productDetail);
    }

    public function syncSingleVariantFromJubelioDetail(array $productDetail, int $jubelioItemId): void
    {
        DB::transaction(function () use ($productDetail, $jubelioItemId) {
            Log::info('Syncing single variant from Jubelio', ['jubelio_item_id' => $jubelioItemId]);

            // Create a synthetic summary for the core product data sync
            $productSummary = [
                'item_group_id' => $productDetail['item_group_id'],
            ];

            $product = $this->syncCoreProductData($productSummary, $productDetail);
            $this->syncAttributesAndValues($productDetail);

            $variantData = collect($productDetail['product_skus'])->firstWhere('item_id', $jubelioItemId);

            if ($variantData) {
                // Create a synthetic summary for the specific variant, fetching stock info
                $stocks = $this->jubelioProductService->getProductAllStock([$jubelioItemId]);
                $stockData = collect($stocks['data'] ?? [])->firstWhere('item_id', $jubelioItemId);

                $summaryVariant = [
                    'item_id' => $jubelioItemId,
                    'item_name' => $stockData['item_name'] ?? null,
                    'available_qty' => $stockData['total_stocks']['available'] ?? 0,
                    'end_qty' => $stockData['total_stocks']['on_hand'] ?? 0,
                    'order_qty' => $stockData['total_stocks']['on_order'] ?? 0,
                ];

                $variant = $this->updateOrCreateVariant($product, $variantData, $summaryVariant, $productDetail);
                $this->syncVariantAttributeValues($variant, $variantData);
                $this->syncVariantImages($variant, $variantData);
            } else {
                Log::warning('Variant data not found in product detail response.', [
                    'jubelio_item_id' => $jubelioItemId,
                    'item_group_id' => $productDetail['item_group_id']
                ]);
            }
        });
    }

    private function syncProduct(array $productSummary, array $productDetail): void
    {
        DB::transaction(function () use ($productSummary, $productDetail) {
            Log::info('Syncing product from Jubelio', ['item_group_id' => $productDetail['item_group_id']]);

            $product = $this->syncCoreProductData($productSummary, $productDetail);
            $this->syncAttributesAndValues($productDetail);

            $existingAttributeValueIds = $product->variants()->with('attributeValues')->get()->pluck('attributeValues.*.id')->flatten()->unique();
            $existingAttributeIds = AttributeValue::whereIn('id', $existingAttributeValueIds)->pluck('attribute_id')->unique();

            $allCurrentAttributeValueIds = $this->syncVariants($product, $productSummary, $productDetail);

            $this->cleanupOrphanedAttributesAndValues($product, $existingAttributeValueIds, $existingAttributeIds, $allCurrentAttributeValueIds);
        });
    }

    private function syncCoreProductData(array $productSummary, array $productDetail): Product
    {
        $existingProduct = Product::where('jubelio_item_group_id', $productDetail['item_group_id'])->first();
        $oldThumbnailUrl = $existingProduct?->thumbnail_original_url;
        $oldThumbnailPath = $existingProduct?->thumbnail_path;

        /** @var Product $product */
        $product = Product::updateOrCreate(
            ['jubelio_item_group_id' => $productDetail['item_group_id']],
            [
                'name' => $productDetail['item_group_name'],
                'description' => $productDetail['description'],
                'brand_name' => $productDetail['brand_name'],
                'thumbnail_original_url' => $productSummary['thumbnail'] ?? $existingProduct?->thumbnail_original_url,
                'is_consignment' => $productSummary['is_consignment'] ?? $existingProduct->is_consignment ?? false,
                'package_height' => $productDetail['package_height'],
                'package_width' => $productDetail['package_width'],
                'package_length' => $productDetail['package_length'],
            ]
        );

        $newThumbnailUrl = $product->thumbnail_original_url;

        if ($oldThumbnailUrl && $oldThumbnailUrl !== $newThumbnailUrl && $oldThumbnailPath) {
            // Clear old thumbnail data and dispatch deletion job
            $product->update([
                'thumbnail_path' => null,
                'thumbnail_original_url' => null,
            ]);
            // Find the corresponding Image model to delete
            $imageToDelete = $existingProduct->images()->where('path', $oldThumbnailPath)->first();
            if ($imageToDelete) {
                /** @var Image $imageToDelete */
                DeleteImageRecordAndFile::dispatch($imageToDelete);
                Log::info('Dispatched job to delete old product thumbnail.', ['path' => $oldThumbnailPath]);
            }
            Log::info('Dispatched job to delete old product thumbnail.', ['path' => $oldThumbnailPath]);
        }

        if ($newThumbnailUrl) {
            $originalImageUrl = str_replace('_thumb.', '.', $newThumbnailUrl);

            // --- BACKFILL LOGIC ---
            // Find the existing image record for the thumbnail.
            $thumbnailImage = $product->images()->where('original_url', $newThumbnailUrl)->first();

            // If the record exists but is missing the original_image_url, update it.
            // This ensures that re-syncs will backfill the URL for old records.
            /** @var \App\Models\Image|null $thumbnailImage */
            if ($thumbnailImage && !$thumbnailImage->original_image_url) {
                $thumbnailImage->update(['original_image_url' => $originalImageUrl]);
            }
            // --- END BACKFILL LOGIC ---

            /** @var Image|null $imageRecord */
            $imageRecord = $product->images()->whereThumbnailOrOriginalUrl($newThumbnailUrl, $originalImageUrl)->first();

            // We need to download if the record doesn't exist, OR if it exists but a file path is missing.
            $needsDownload = !$imageRecord || is_null($imageRecord->path) || ($originalImageUrl && is_null($imageRecord->original_image_path));

            if ($needsDownload) {
                // Backfill original_image_url if it's missing on an existing record
                if ($imageRecord && !$imageRecord->original_image_url && $originalImageUrl) {
                    $imageRecord->update(['original_image_url' => $originalImageUrl]);
                }
                DownloadImage::dispatch(Product::class, $product->id, $newThumbnailUrl, 0, $originalImageUrl);
            }
        }

        return $product;
    }

    private function syncAttributesAndValues(array $productDetail): void
    {
        foreach (($productDetail['variations'] ?? []) as $variation) {
            $attribute = Attribute::firstOrCreate(['name' => $variation['label']]);
            foreach ($variation['values'] as $value) {
                $attribute->values()->firstOrCreate(['value' => $value]);
            }
        }
    }

    private function syncVariants(Product $product, array $productSummary, array $productDetail): \Illuminate\Support\Collection
    {
        $summaryVariants = collect($productSummary['variants'])->keyBy('item_id');
        $allCurrentAttributeValueIds = collect();

        foreach ($productDetail['product_skus'] as $variantData) {
            $variant = $this->updateOrCreateVariant($product, $variantData, $summaryVariants->get($variantData['item_id']), $productDetail);
            $attributeValueIds = $this->syncVariantAttributeValues($variant, $variantData);
            $allCurrentAttributeValueIds = $allCurrentAttributeValueIds->merge($attributeValueIds);
            $this->syncVariantImages($variant, $variantData);
        }

        return $allCurrentAttributeValueIds;
    }

    private function updateOrCreateVariant(Product $product, array $variantData, ?array $summaryVariant, array $productDetail): ProductVariant
    {
        /** @var ProductVariant $variant */
        $variant = $product->variants()->updateOrCreate(
            ['jubelio_item_id' => $variantData['item_id']],
            [
                'sku' => $variantData['item_code'],
                'name' => $summaryVariant['item_name'] ?? $product->name,
                'sell_price' => $variantData['sell_price'],
                'weight' => $productDetail['package_weight'],
                'barcode' => $variantData['barcode'],
                'available_qty' => max(0, $summaryVariant['available_qty'] ?? 0),
                'on_hand_qty' => $summaryVariant['end_qty'] ?? 0,
                'on_order_qty' => $summaryVariant['order_qty'] ?? 0,
            ]
        );

        return $variant;
    }

    private function syncVariantAttributeValues(ProductVariant $variant, array $variantData): array
    {
        $attributeValueIds = [];
        foreach (($variantData['variation_values'] ?? []) as $variationValue) {
            $attribute = Attribute::firstOrCreate(['name' => $variationValue['label']]);
            $attributeValue = $attribute->values()->firstOrCreate(['value' => $variationValue['value']]);
            /** @var AttributeValue $attributeValue */
            $attributeValueIds[] = $attributeValue->id;
        }
        $variant->attributeValues()->sync($attributeValueIds);
        return $attributeValueIds;
    }

    private function syncVariantImages(ProductVariant $variant, array $variantData): void
    {
        $incomingImageUrls = collect($variantData['images'])->pluck('cloud_key')->filter()->all();
        $existingImages = $variant->images;
        $existingImageUrls = $existingImages->pluck('original_image_url')->filter()->all();

        // Fallback for older data that might not have original_image_url
        $legacyImageUrls = $existingImages->whereNull('original_image_url')->pluck('original_url')->all();

        $allExistingUrls = array_merge($existingImageUrls, $legacyImageUrls);

        $urlsToDelete = array_diff($allExistingUrls, $incomingImageUrls);

        if (!empty($urlsToDelete)) {
            $imagesToDelete = $existingImages->filter(function ($image) use ($urlsToDelete) {
                return in_array($image->original_image_url, $urlsToDelete) || in_array($image->original_url, $urlsToDelete);
            });
            foreach ($imagesToDelete as $imageModel) {
                if ($imageModel->path) {
                    DeleteImageRecordAndFile::dispatch($imageModel);
                }
            }
            Log::info('Deleted orphaned images for variant.', ['variant_id' => $variant->id, 'deleted_urls' => $urlsToDelete]);
        }

        foreach ($variantData['images'] as $imageData) {
            if (!empty($imageData['cloud_key'])) {
                // Use the explicit thumbnail and cloud_key URLs provided by the API
                $thumbnailUrl = $imageData['thumbnail'] ?? null;
                $originalImageUrl = $imageData['cloud_key'] ?? null;

                // Ensure we have a URL to process before dispatching the job
                if ($thumbnailUrl) {
                    // Check if an image with this original_url or original_image_url already exists for this variant.
                    /** @var Image|null $imageRecord */
                    $imageRecord = $variant->images()->whereThumbnailOrOriginalUrl($thumbnailUrl, $originalImageUrl)->first();

                    // We need to download if the record doesn't exist, OR if it exists but a file path is missing.
                    $needsDownload = !$imageRecord || is_null($imageRecord->path) || ($originalImageUrl && is_null($imageRecord->original_image_path));

                    if ($needsDownload) {
                        // Backfill original_image_url if it's missing on an existing record
                        if ($imageRecord && !$imageRecord->original_image_url && $originalImageUrl) {
                            $imageRecord->update(['original_image_url' => $originalImageUrl]);
                        }

                        DownloadImage::dispatch(
                            ProductVariant::class,
                            $variant->id,
                            $thumbnailUrl,
                            $imageData['sequence_number'] ?? 0,
                            $originalImageUrl // Pass the original URL if it exists
                        );
                    }
                }
            }
        }
    }

    private function cleanupOrphanedAttributesAndValues(Product $product, \Illuminate\Support\Collection $existingAttributeValueIds, \Illuminate\Support\Collection $existingAttributeIds, \Illuminate\Support\Collection $allCurrentAttributeValueIds): void
    {
        $orphanedAttributeValueIds = $existingAttributeValueIds->diff($allCurrentAttributeValueIds->unique());
        if ($orphanedAttributeValueIds->isNotEmpty()) {
            AttributeValue::destroy($orphanedAttributeValueIds);
            Log::info('Deleted orphaned attribute values.', ['ids' => $orphanedAttributeValueIds->all()]);
        }

        $currentAttributeIds = AttributeValue::whereIn('id', $allCurrentAttributeValueIds->unique())->pluck('attribute_id')->unique();
        $orphanedAttributeIds = $existingAttributeIds->diff($currentAttributeIds);

        $attributesInUseByOtherProducts = DB::table('product_variant_attribute_values')
            ->whereIn('attribute_value_id', function ($query) use ($orphanedAttributeIds) {
                $query->select('id')->from('attribute_values')->whereIn('attribute_id', $orphanedAttributeIds);
            })
            ->join('product_variants', 'product_variant_attribute_values.product_variant_id', '=', 'product_variants.id')
            ->where('product_variants.product_id', '!=', $product->id)
            ->exists();

        if ($orphanedAttributeIds->isNotEmpty() && !$attributesInUseByOtherProducts) {
            Attribute::destroy($orphanedAttributeIds);
            Log::info('Deleted orphaned attributes.', ['ids' => $orphanedAttributeIds->all()]);
        }
    }
}
