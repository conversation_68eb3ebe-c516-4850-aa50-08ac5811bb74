<?php

namespace App\Services;

use App\Models\SyncLog;

class SyncLoggerService
{
    public function log(string $level, string $message, string $syncType, array $context = [])
    {
        SyncLog::create([
            'level' => $level,
            'message' => $message,
            'sync_type' => $syncType,
            'reference_id' => $context['reference_id'] ?? null,
            'reference_type' => $context['reference_type'] ?? null,
            'context' => $context,
        ]);
    }

    public function info(string $message, string $syncType, array $context = [])
    {
        $this->log(SyncLog::LEVEL_INFO, $message, $syncType, $context);
    }

    public function error(string $message, string $syncType, array $context = [])
    {
        $this->log(SyncLog::LEVEL_ERROR, $message, $syncType, $context);
    }

    public function warning(string $message, string $syncType, array $context = [])
    {
        $this->log(SyncLog::LEVEL_WARNING, $message, $syncType, $context);
    }
}
