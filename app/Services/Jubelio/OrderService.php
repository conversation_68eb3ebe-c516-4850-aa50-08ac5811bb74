<?php

namespace App\Services\Jubelio;

use Illuminate\Support\Facades\Log;

class OrderService extends BaseJubelioService
{
    /**
     * Set an order as paid in Jubelio.
     *
     * @param int $jubelioOrderId
     * @return array|null
     */
    public function setOrderAsPaid(int $jubelioOrderId): ?array
    {
        try {
            $payload = ['ids' => [(string) $jubelioOrderId]];
            $response = $this->client()->post('/sales/orders/set-as-paid', $payload);

            $responseData = $response->json();
            Log::info("Successfully set order {$jubelioOrderId} as paid in Jubelio.", [
                'request' => $payload,
                'response' => $responseData,
            ]);
            return $responseData;
        } catch (\Illuminate\Http\Client\RequestException $e) {
            Log::error("Failed to set order {$jubelioOrderId} as paid in Jubelio.", [
                'request' => ['ids' => [(string) $jubelioOrderId]],
                'response_status' => $e->response->status(),
                'response_body' => $e->response->json() ?? $e->response->body(),
            ]);
            return null;
        }
    }

    /**
     * Get sales order detail from Jubelio.
     *
     * @param int $jubelioOrderId
     * @return array|null
     */
    public function getSalesOrderDetail(int $jubelioOrderId): ?array
    {
        try {
            $response = $this->client()->get("/sales/orders/{$jubelioOrderId}");

            $responseData = $response->json();
            Log::info("Successfully retrieved sales order {$jubelioOrderId} from Jubelio.", [
                'response' => $responseData,
            ]);
            return $responseData;
        } catch (\Illuminate\Http\Client\RequestException $e) {
            Log::error("Failed to retrieve sales order {$jubelioOrderId} from Jubelio.", [
                'response_status' => $e->response->status(),
                'response_body' => $e->response->json() ?? $e->response->body(),
            ]);
            return null;
        }
    }

    /**
     * Create or update a sales order in Jubelio.
     * The same endpoint is used for both operations.
     *
     * @param array $salesOrderData
     * @return array|null
     */
    public function createOrUpdateSalesOrder(array $salesOrderData): ?array
    {
        $isUpdate = !empty($salesOrderData['salesorder_id']);
        // Per user instruction, the endpoint is the same for create and update.
        // No leading slash to prevent double slashes with base_url
        $url = '/sales/orders/';

        try {
            if (!$isUpdate) {
                // Set defaults for creation
                $salesOrderData['salesorder_id'] = 0;
                $salesOrderData['salesorder_no'] = '[auto]';
            }

            $response = $this->client()->post($url, $salesOrderData);

            if ($response->failed()) {
                $action = $isUpdate ? 'update' : 'create';
                $id = $salesOrderData['salesorder_id'] ?? 'N/A';
                Log::error("Failed to {$action} sales order {$id} in Jubelio.", [
                    'request' => $salesOrderData,
                    'response_status' => $response->status(),
                    'response_body' => $response->json() ?? $response->body(),
                    'response_headers' => $response->headers(),
                ]);
                return null;
            }

            $responseData = $response->json();

            if ($isUpdate) {
                Log::info("Successfully updated sales order {$salesOrderData['salesorder_id']} in Jubelio.", [
                    'request' => $salesOrderData,
                    'response' => $responseData,
                ]);
            } else {
                Log::info("Successfully created a new sales order in Jubelio.", [
                    'request' => $salesOrderData,
                    'response' => $responseData,
                ]);
            }

            return $responseData;
        } catch (\Exception $e) {
            $action = $isUpdate ? 'update' : 'create';
            $id = $salesOrderData['salesorder_id'] ?? 'N/A';
            Log::error("Exception when trying to {$action} sales order {$id} in Jubelio.", [
                'request' => $salesOrderData,
                'exception_message' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Set sales orders as ready to pick in Jubelio.
     *
     * @param array $salesOrderIds
     * @return array|null
     */
    public function setReadyToPick(array $salesOrderIds): ?array
    {
        try {
            // Ensure all IDs are integers.
            $payload = ['salesorder_ids' => array_map('intval', $salesOrderIds)];
            $response = $this->client()->post('/wms/sales/ready-to-pick', $payload);

            if ($response->failed()) {
                Log::error('Failed to set sales orders as ready to pick in Jubelio.', [
                    'request' => $payload,
                    'response_status' => $response->status(),
                    'response_body' => $response->json() ?? $response->body(),
                ]);
                return null;
            }

            $responseData = $response->json();
            Log::info('Successfully set sales orders as ready to pick in Jubelio.', [
                'request' => $payload,
                'response' => $responseData,
            ]);
            return $responseData;
        } catch (\Illuminate\Http\Client\RequestException $e) {
            Log::error('RequestException when setting sales orders as ready to pick in Jubelio.', [
                'request' => ['salesorder_ids' => array_map('strval', $salesOrderIds)],
                'response_status' => $e->response->status(),
                'response_body' => $e->response->json() ?? $e->response->body(),
            ]);
            return null;
        } catch (\Exception $e) {
            Log::error('Exception when setting sales orders as ready to pick in Jubelio.', [
                'request' => ['salesorder_ids' => array_map('strval', $salesOrderIds)],
                'exception_message' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Check if a sales order is ready to be processed in Jubelio.
     *
     * @param int $jubelioOrderId
     * @return bool
     */
    public function isOrderReadyToProcess(int $jubelioOrderId): bool
    {
        try {
            $response = $this->client()->get('/wms/sales/orders/ready-to-process/', [
                'page' => 1,
                'pageSize' => 1,
                'q' => $jubelioOrderId,
            ]);

            if ($response->failed()) {
                Log::warning("Jubelio API check for ready-to-process failed for order {$jubelioOrderId}.", [
                    'response_status' => $response->status(),
                    'response_body' => $response->json() ?? $response->body(),
                ]);
                return false;
            }

            $data = $response->json();
            $isReady = isset($data['totalCount']) && $data['totalCount'] > 0;

            Log::info("Jubelio ready-to-process check for order {$jubelioOrderId}: " . ($isReady ? 'Ready' : 'Not Ready'), [
                'response' => $data,
            ]);

            return $isReady;
        } catch (\Exception $e) {
            Log::error("Exception during Jubelio ready-to-process check for order {$jubelioOrderId}.", [
                'exception_message' => $e->getMessage(),
            ]);
            return false;
        }
    }
}
