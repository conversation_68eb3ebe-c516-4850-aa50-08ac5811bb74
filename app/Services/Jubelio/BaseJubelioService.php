<?php

namespace App\Services\Jubelio;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

abstract class BaseJubelioService
{
    protected $base_url;
    protected $email;
    protected $password;
    protected $secret_key;

    public function __construct()
    {
        $this->base_url = config('jubelio.base_url');
        $this->email = config('jubelio.email');
        $this->password = config('jubelio.password');
        $this->secret_key = config('jubelio.secret_key');
    }

    /**
     * Login to get token
     *
     * @return string|null
     */
    public function loginToGetToken()
    {
        $url = $this->base_url . '/login';
        $response = Http::post($url, [
            'email' => $this->email,
            'password' => $this->password
        ]);

        if ($response->failed()) {
            Log::error('Login failed: ' . $response->body());
            return null;
        }

        $data = $response->json();
        $token = $data['token'];

        Cache::put('jubelio_token', $token, now()->addHours(11));

        return $token;
    }

    /**
     * Get token from cache or login to get token
     *
     * @return string
     */
    protected function getToken()
    {
        $token = Cache::get('jubelio_token');

        if (!$token) {
            $token = $this->loginToGetToken();
        }

        return $token;
    }

    /**
     * Get a pre-configured HTTP client instance for Jubelio.
     *
     * @return \Illuminate\Http\Client\PendingRequest
     */
    protected function client()
    {
        return Http::withToken($this->getToken())
            ->baseUrl($this->base_url)
            ->contentType('application/json')
            ->acceptJson();
    }
}
