<?php

namespace App\Services\Jubelio;

use Illuminate\Support\Facades\Log;

class ProductService extends BaseJubelioService
{
    /**
     * Get a list of products from Jubelio.
     *
     * @param array $params
     *   - `page` (int) required
     *   - `pageSize` (int) required
     *   - `sortDirection` (string) optional
     *   - `sortBy` (string) optional
     *   - `q` (string) optional
     * @return array|null
     */
    public function getProducts(array $params = []): ?array
    {
        try {
            $response = $this->client()->get('/inventory/items/', $params);

            if ($response->failed()) {
                Log::error('Failed to retrieve products from Jubelio.', [
                    'response_status' => $response->status(),
                    'response_body' => $response->json() ?? $response->body(),
                ]);
                return null;
            }

            $responseData = $response->json();
            Log::info('Successfully retrieved products from Jubelio.', [
                'params' => $params,
                'count' => count($responseData['data'] ?? []),
            ]);

            return $responseData;
        } catch (\Exception $e) {
            Log::error('Exception when trying to retrieve products from Jubelio.', [
                'exception_message' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Get all stock for a list of product IDs.
     *
     * @param array $ids
     * @return array|null
     */
    public function getProductAllStock(array $ids): ?array
    {
        try {
            $response = $this->client()->post('/inventory/items/all-stocks/', [
                'ids' => $ids,
            ]);

            if ($response->failed()) {
                Log::error('Failed to retrieve product stock from Jubelio.', [
                    'response_status' => $response->status(),
                    'response_body' => $response->json() ?? $response->body(),
                ]);
                return null;
            }

            $responseData = $response->json();
            Log::info('Successfully retrieved product stock from Jubelio.', [
                'ids_count' => count($ids),
                'data_count' => count($responseData['data'] ?? []),
            ]);

            return $responseData;
        } catch (\Exception $e) {
            Log::error('Exception when trying to retrieve product stock from Jubelio.', [
                'exception_message' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Get product detail from Jubelio.
     *
     * @param int $itemId
     * @return array|null
     */
    public function getProductDetail(int $itemId): ?array
    {
        try {
            $response = $this->client()->get("/inventory/items/{$itemId}");

            if ($response->failed()) {
                Log::error("Failed to retrieve product detail for item {$itemId} from Jubelio.", [
                    'response_status' => $response->status(),
                    'response_body' => $response->json() ?? $response->body(),
                ]);
                return null;
            }

            $responseData = $response->json();
            Log::info("Successfully retrieved product detail for item {$itemId} from Jubelio.", [
                'item_id' => $itemId,
            ]);

            return $responseData;
        } catch (\Exception $e) {
            Log::error("Exception when trying to retrieve product detail for item {$itemId} from Jubelio.", [
                'exception_message' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Get product detail from Jubelio by item group id.
     *
     * @param int $itemGroupId
     * @return array|null
     */
    public function getProductDetailByItemGroupId(int $itemGroupId): ?array
    {
        try {
            $response = $this->client()->get("/inventory/items/group/{$itemGroupId}");

            if ($response->failed()) {
                Log::error("Failed to retrieve product detail for item group {$itemGroupId} from Jubelio.", [
                    'response_status' => $response->status(),
                    'response_body' => $response->json() ?? $response->body(),
                ]);
                return null;
            }

            $responseData = $response->json();
            Log::info("Successfully retrieved product detail for item group {$itemGroupId} from Jubelio.", [
                'item_group_id' => $itemGroupId,
            ]);

            return $responseData;
        } catch (\Exception $e) {
            Log::error("Exception when trying to retrieve product detail for item group {$itemGroupId} from Jubelio.", [
                'exception_message' => $e->getMessage(),
            ]);
            return null;
        }
    }
}
