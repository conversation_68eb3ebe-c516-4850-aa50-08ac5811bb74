<?php

namespace App\Services\Jubelio;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class WebhookService extends BaseJubelioService
{
    /**
     * Verify the webhook signature from Jubelio.
     *
     * @param \Illuminate\Http\Request $request
     * @return bool
     */
    public function verifyWebhookSignature(Request $request): bool
    {
        $jubelioSignature = $request->header('sign');

        if (!$jubelioSignature) {
            Log::warning('Jubelio webhook signature not found.');
            return false;
        }

        $payload = trim($request->getContent());
        $content = $payload . $this->secret_key;
        $calculatedSignature = hash_hmac('sha256', $content, $this->secret_key, false);
        $isValid = hash_equals($calculatedSignature, $jubelioSignature);
        $payloadArray = json_decode($payload, true);

        Log::info('Jubelio Webhook Signature Verification Success', [
            'store_name' => $payloadArray['store_name'] ?? 'not_found',
            'channel_status' => $payloadArray['channel_status'] ?? 'not_found',
        ]);

        if (!$isValid) {
            Log::warning('Jubelio webhook signature validation failed.');
        }

        return $isValid;
    }
}
