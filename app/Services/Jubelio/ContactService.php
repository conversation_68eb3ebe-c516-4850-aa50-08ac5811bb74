<?php

namespace App\Services\Jubelio;

use App\Models\User;
use Illuminate\Support\Facades\Log;

class ContactService extends BaseJubelioService
{
    /**
     * Create a new user in Jubelio.
     *
     * @param \App\Models\User $user
     * @return array|null
     */
    public function createUser(User $user): ?array
    {
        $payload = [
            'contact_id' => 0,
            'contact_name' => $user->name,
            'contact_type' => 0, // 0 for customer default
            'phone' => $user->phone,
            'email' => $user->email,
            'is_reseller' => $user->role === 'reseller', // is_reseller must true to keep track data
        ];

        try {
            $response = $this->client()->post('/contacts/', $payload);

            if ($response->failed()) {
                Log::error("Failed to create contact in Jubelio for user: {$user->email}", [
                    'request' => $payload,
                    'response_status' => $response->status(),
                    'response_body' => $response->json() ?? $response->body(),
                ]);
                return null;
            }

            $responseData = $response->json();
            Log::info("Successfully created contact in Jubelio for user: {$user->email}", [
                'request' => $payload,
                'response' => $responseData,
            ]);

            return $responseData;

        } catch (\Exception $e) {
            Log::error("Exception when trying to create contact in Jubelio for user: {$user->email}", [
                'request' => $payload,
                'exception_message' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Create or update a contact in Jubelio.
     *
     * @param array $contactData The contact data from our system.
     * @param int|null $jubelioContactId The existing Jubelio contact ID for updates.
     * @return array|null
     */
    public function createOrUpdateContact(array $contactData, ?int $jubelioContactId = null): ?array
    {
        $isUpdate = $jubelioContactId !== null;

        $payload = [
            'contact_id' => $isUpdate ? $jubelioContactId : 0,
            'contact_type' => 0, // 0 for customer default
            'is_reseller' => true, // Always true as per user instruction
        ];

        // Dynamically build the payload to handle partial updates correctly.
        // Only add fields to the payload if they exist in the input data.
        if (array_key_exists('name', $contactData)) {
            $payload['contact_name'] = $contactData['name'];
        }
        if (array_key_exists('phone', $contactData)) {
            $payload['phone'] = $contactData['phone'];
        }
        if (array_key_exists('email', $contactData)) {
            $payload['email'] = $contactData['email'];
        }

        // For creation, Jubelio might require all fields. If any are missing,
        // we ensure they are present in the payload, even if null.
        if (!$isUpdate) {
            $payload['contact_name'] = $contactData['name'] ?? null;
            $payload['phone'] = $contactData['phone'] ?? null;
            $payload['email'] = $contactData['email'] ?? null;
        }

        try {
            $response = $this->client()->post('/contacts/', $payload);

            if ($response->failed()) {
                $action = $isUpdate ? 'update' : 'create';
                Log::error("Failed to {$action} contact in Jubelio.", [
                    'request' => $payload,
                    'response_status' => $response->status(),
                    'response_body' => $response->json() ?? $response->body(),
                ]);
                return null;
            }

            $responseData = $response->json();
            $action = $isUpdate ? 'Updated' : 'Created';
            Log::info("Successfully {$action} contact in Jubelio: {$payload['contact_name']}", [
                'request' => $payload,
                'response' => $responseData,
            ]);

            return $responseData;

        } catch (\Exception $e) {
            $action = $isUpdate ? 'update' : 'create';
            Log::error("Exception when trying to {$action} contact in Jubelio.", [
                'request' => $payload,
                'exception_message' => $e->getMessage(),
            ]);
            return null;
        }
    }
    /**
     * Delete a contact in Jubelio.
     *
     * @param int $contactId The Jubelio contact ID.
     * @return array|null
     */
    public function deleteContact(int $contactId): ?array
    {
        try {
            $payload = ['ids' => [$contactId]];
            $response = $this->client()->delete('/contacts/pelanggan', $payload);

            if ($response->failed()) {
                Log::error("Failed to delete contact in Jubelio.", [
                    'request' => $payload,
                    'response_status' => $response->status(),
                    'response_body' => $response->json() ?? $response->body(),
                ]);
                return null;
            }

            $responseData = $response->json();
            Log::info("Successfully deleted contact {$contactId} in Jubelio.", [
                'request' => $payload,
                'response' => $responseData,
            ]);

            return $responseData;

        } catch (\Exception $e) {
            Log::error("Exception when trying to delete contact {$contactId} in Jubelio.", [
                'request' => ['ids' => [$contactId]],
                'exception_message' => $e->getMessage(),
            ]);
            return null;
        }
    }
}
