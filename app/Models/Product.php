<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

/**
 * @mixin IdeHelperProduct
 */
class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'jubelio_item_group_id',
        'name',
        'description',
        'brand_name',
        'thumbnail_path',
        'thumbnail_original_url',
        'is_consignment',
        'package_height',
        'package_width',
        'package_length',
    ];

    public function variants(): HasMany
    {
        return $this->hasMany(ProductVariant::class);
    }

    public function images(): MorphMany
    {
        return $this->morphMany(Image::class, 'imageable');
    }
    /**
     * Get the min and max price for the product's variants.
     *
     * @return array
     */
    public function getPriceDataAttribute(): array
    {
        if ($this->relationLoaded('variants') && $this->variants->isNotEmpty()) {
            return [
                'min' => $this->variants->min('sell_price'),
                'max' => $this->variants->max('sell_price'),
            ];
        }

        return ['min' => null, 'max' => null];
    }
}
