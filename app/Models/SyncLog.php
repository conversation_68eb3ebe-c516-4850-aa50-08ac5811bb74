<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @mixin IdeHelperSyncLog
 */
class SyncLog extends Model
{
    use HasFactory;

    public const LEVEL_INFO = 'info';
    public const LEVEL_WARNING = 'warning';
    public const LEVEL_ERROR = 'error';

    public const TYPE_MANUAL_FULL = 'manual_full_sync';
    public const TYPE_SCHEDULED_FULL = 'scheduled_full_sync';
    public const TYPE_WEBHOOK = 'webhook_product';
    public const TYPE_MANUAL_SINGLE = 'manual_single_product';
    public const TYPE_MANUAL_SINGLE_VARIANT = 'manual_single_variant';
    public const TYPE_IMAGE_DOWNLOAD = 'image_download';
    public const TYPE_THUMBNAIL_IMAGE_DOWNLOAD = 'thumbnail_image_download';
    public const TYPE_VARIANT_IMAGE_DOWNLOAD = 'variant_image_download';
    public const TYPE_WEBHOOK_STOCK = 'webhook_stock';
    public const TYPE_ORIGINAL_IMAGE_DOWNLOAD = 'original_image_download';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'level',
        'message',
        'sync_type',
        'reference_id',
        'reference_type',
        'context',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'context' => 'array',
    ];

    public static function getLevels(): array
    {
        return [
            self::LEVEL_INFO,
            self::LEVEL_WARNING,
            self::LEVEL_ERROR,
        ];
    }

    public static function getSyncTypes(): array
    {
        return [
            self::TYPE_MANUAL_FULL,
            self::TYPE_SCHEDULED_FULL,
            self::TYPE_WEBHOOK,
            self::TYPE_MANUAL_SINGLE,
            self::TYPE_MANUAL_SINGLE_VARIANT,
            self::TYPE_IMAGE_DOWNLOAD,
            self::TYPE_THUMBNAIL_IMAGE_DOWNLOAD,
            self::TYPE_VARIANT_IMAGE_DOWNLOAD,
            self::TYPE_WEBHOOK_STOCK,
            self::TYPE_ORIGINAL_IMAGE_DOWNLOAD,
        ];
    }
}
