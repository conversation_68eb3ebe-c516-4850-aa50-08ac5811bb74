<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @mixin IdeHelperImage
 */
class Image extends Model
{
    use HasFactory;

    protected $fillable = [
        'path',
        'original_url',
        'original_image_url',
        'original_image_path',
        'sequence_number',
    ];

    public function imageable(): MorphTo
    {
        return $this->morphTo();
    }
    /**
     * Scope a query to find an image by its thumbnail URL or original image URL.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $thumbnailUrl
     * @param string|null $originalImageUrl
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWhereThumbnailOrOriginalUrl(\Illuminate\Database\Eloquent\Builder $query, string $thumbnailUrl, ?string $originalImageUrl): \Illuminate\Database\Eloquent\Builder
    {
        return $query->where(function ($q) use ($thumbnailUrl, $originalImageUrl) {
            $q->where('original_url', $thumbnailUrl)
                ->orWhere('original_image_url', $originalImageUrl);
        });
    }
}
