<?php

namespace App\Console\Commands;

use App\Models\Image;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class DuplicateImageData
{
    public function __construct(
        public int $imageable_id,
        public string $imageable_type,
        public string $effective_url,
        public int $count
    ) {
    }
}

class FindDuplicateImages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'otosync:find-duplicate-images
                            {--delete : Delete the duplicate records, keeping the newest one.}
                            {--force : Force delete without asking for confirmation.}
                            {--silent : Do not show the details of each duplicate set.}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Find duplicate image records based on original_image_url (or original_url as fallback) for the same product/variant.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to find duplicate images...');

        // We use COALESCE to prioritize original_image_url and fall back to original_url.
        // The result is aliased as effective_url to make it accessible in the result set.
        $rawDuplicates = Image::query()
            ->toBase()
            ->select('imageable_id', 'imageable_type', DB::raw('COALESCE(original_image_url, original_url) as effective_url'), DB::raw('COUNT(*) as count'))
            ->groupBy('imageable_id', 'imageable_type', 'effective_url')
            ->having('count', '>', 1)
            ->get()
            ->all();

        $duplicates = collect(array_map(fn ($row) => new DuplicateImageData(...(array) $row), $rawDuplicates));

        if ($duplicates->isEmpty()) {
            $this->info('No duplicate images found.');
            return self::SUCCESS;
        }

        $this->warn(sprintf('Found %d sets of duplicate images.', $duplicates->count()));

        $totalDeleted = 0;
        $delete = $this->option('delete');
        $force = $this->option('force');
        $isSilent = $this->option('silent');

        if ($delete && !$force && !$isSilent && !$this->confirm(sprintf('About to delete duplicates for %d sets of images. Are you sure?', $duplicates->count()))) {
            $this->info('Deletion cancelled.');
            return self::SUCCESS;
        }

        /** @var DuplicateImageData $duplicate */
        foreach ($duplicates as $duplicate) {

            $images = Image::where('imageable_id', $duplicate->imageable_id)
                ->where('imageable_type', $duplicate->imageable_type)
                ->where(function ($query) use ($duplicate) {
                    $query->where('original_image_url', $duplicate->effective_url)
                          ->orWhere('original_url', $duplicate->effective_url);
                })
                ->get();

            if (!$isSilent) {
                $this->line('');
                $this->line(sprintf(
                    'Found %d duplicates for URL: %s',
                    $duplicate->count,
                    $duplicate->effective_url
                ));
                $this->line(sprintf(
                    'Associated with %s ID: %d',
                    class_basename($duplicate->imageable_type),
                    $duplicate->imageable_id
                ));
                $this->table(
                    ['ID', 'Path', 'Created At'],
                    $images->map(fn (Image $image) => [$image->id, $image->path, $image->created_at->toDateTimeString()])
                );
            }

            if ($delete) {
                $newestImage = $images->sortByDesc('created_at')->first();
                $imagesToDelete = $images->where('id', '!=', $newestImage->id);

                if ($force || $isSilent || $this->confirm(sprintf('About to delete %d image record(s). Are you sure?', $imagesToDelete->count()))) {
                    $count = $imagesToDelete->count();
                    // We won't delete the physical file, as it might be a duplicate entry for a valid file.
                    Image::destroy($imagesToDelete->pluck('id'));
                    $totalDeleted += $count;
                    if (!$isSilent) {
                        $this->info(sprintf('Deleted %d image record(s).', $count));
                    }
                } else {
                    // if (!$isSilent) {
                    //     $this->info('Deletion cancelled for this set.');
                    // }
                    $this->info('Deletion cancelled for this set.');
                }
            }
        }

        if ($delete) {
            $this->info(sprintf('Total image records deleted: %d', $totalDeleted));
        }

        $this->info('Finished finding duplicate images.');
        return self::SUCCESS;
    }
}
