<?php

namespace App\Console\Commands;

use App\Jobs\DownloadImage;
use App\Jobs\DownloadOriginalImage;
use App\Models\Image;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class RedownloadMissingImages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'otosync:redownload-missing-images';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Queue jobs to download images that are missing for products and variants. e.g: php artisan otosync:redownload-missing-images';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to queue jobs for missing images...');

        $this->info('Queueing jobs for missing thumbnail images...');
        Image::whereNull('path')
            ->whereNotNull('original_url')
            ->has('imageable')
            ->each(function (Image $image) {
                DownloadImage::dispatch($image->imageable_type, $image->imageable_id, $image->original_url, $image->sequence_number, $image->original_image_url);
                Log::info('Queued thumbnail download for image: ' . $image->id . ' (' . $image->original_url . ')');
            });
        $this->info('Finished queuing jobs for missing thumbnails.');

        $this->info('Queueing jobs for missing original images...');
        Image::whereNull('original_image_path')
            ->whereNotNull('original_image_url')
            ->has('imageable')
            ->each(function (Image $image) {
                DownloadOriginalImage::dispatch($image->id, $image->original_image_url);
                Log::info('Queued original image download for image: ' . $image->id . ' (' . $image->original_image_url . ')');
            });
        $this->info('Finished queuing jobs for missing original images.');

        $this->info('Finished queuing jobs for missing images.');
    }
}
