<?php

namespace App\Console\Commands;

use App\Jobs\SyncJubelioProduct;
use App\Services\Jubelio\ProductService;
use Illuminate\Console\Command;

class TestSyncJubelioProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'otosync:test-sync-jubelio-products {--limit= : The number of products to fetch} {--page= : The page number to fetch}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch a limited number of products from Jubelio and dispatch sync jobs for testing.';

    /**
     * Execute the console command.
     */
    public function handle(ProductService $productService): int
    {
        $limit = (int) $this->option('limit') ?: 1;
        $page = (int) $this->option('page') ?: 1;

        $this->info("Starting Jubelio product test sync for page {$page} with a limit of {$limit} products...");

        $response = $productService->getProducts(['page' => $page, 'pageSize' => $limit, 'sortDirection' => 'DESC', 'sortBy' => 'last_modified']);
        $products = $response['data'] ?? [];

        if (empty($products)) {
            $this->info('No products found to sync.');
            return self::SUCCESS;
        }

        $this->info('Found ' . count($products) . ' products. Dispatching jobs...');

        foreach ($products as $productData) {
            SyncJubelioProduct::dispatch($productData);
            $this->info("Dispatched job for product: {$productData['item_name']}");
        }

        $this->info('Test sync jobs dispatched successfully.');
        return self::SUCCESS;
    }
}
