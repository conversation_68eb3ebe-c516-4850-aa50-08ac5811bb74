<?php

namespace App\Console\Commands;

use App\Services\Jubelio\ProductService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TestJubelioProduct extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'otosync:test-jubelio-product';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the Jubelio Product API integration';

    /**
     * Execute the console command.
     */
    public function handle(ProductService $productService)
    {
        $this->info('Select a Jubelio Product API test to run:');
        $test = $this->choice(
            'Which endpoint would you like to test?',
            ['Get Products', 'Get Product All Stock', 'Get Product Detail', 'Cancel'],
            0
        );

        switch ($test) {
            case 'Get Products':
                $this->testGetProducts($productService);
                break;
            case 'Get Product All Stock':
                $this->testGetProductAllStock($productService);
                break;
            case 'Get Product Detail':
                $this->testGetProductDetail($productService);
                break;
            case 'Cancel':
                $this->info('Operation cancelled.');
                return 0;
        }

        return 0;
    }

    protected function testGetProducts(ProductService $productService)
    {
        $this->info('Testing Get Products endpoint...');
        $page = $this->ask('Enter page number', '1');
        $pageSize = $this->ask('Enter page size', '10');
        $q = $this->ask('Enter search query (optional)');

        $params = [
            'page' => $page,
            'pageSize' => $pageSize,
        ];

        if ($q) {
            $params['q'] = $q;
        }

        $products = $productService->getProducts($params);
        Log::info('Products response:', $products);

        if ($products) {
            $this->info('Successfully retrieved products. Check the logs for the response.');
        } else {
            $this->error('Failed to retrieve products. Check the logs for more details.');
        }
    }

    protected function testGetProductAllStock(ProductService $productService)
    {
        $this->info('Testing Get Product All Stock endpoint...');
        $ids = $this->ask('Enter product IDs (comma-separated)');
        $idsArray = array_map('intval', explode(',', $ids));

        $stocks = $productService->getProductAllStock($idsArray);

        if ($stocks) {
            $this->info('Successfully retrieved product stock. Check the logs for the response.');
        } else {
            $this->error('Failed to retrieve product stock. Check the logs for more details.');
        }
    }

    protected function testGetProductDetail(ProductService $productService)
    {
        $this->info('Testing Get Product Detail endpoint...');
        $itemId = $this->ask('Enter product item ID');

        $product = $productService->getProductDetail((int)$itemId);

        if ($product) {
            $this->info("Successfully retrieved product detail for item {$itemId}. Check the logs for the response.");
        } else {
            $this->error("Failed to retrieve product detail for item {$itemId}. Check the logs for more details.");
        }
    }
}
