<?php

namespace App\Console\Commands;

use App\Jobs\DownloadImage;
use App\Models\Image;
use App\Models\Product;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class OtosyncFixImagePaths extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'otosync:fix-image-paths';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Find images with incorrect paths (ending with a dot or missing extension) and queue a job to re-download them.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to find and fix incorrect image paths...');

        // Find images ending with a dot or with no extension
        $imagesToFix = Image::whereNotNull('original_url')
            ->where(function ($query) {
                $query->where('path', 'like', '%.')
                    ->orWhere('path', 'not like', '%.%');
            })
            ->has('imageable')
            ->get();

        // Also check for products with incorrect thumbnail_path
        $productsToFix = Product::whereNotNull('thumbnail_path')
            ->where(function ($query) {
                $query->where('thumbnail_path', 'like', '%.')
                    ->orWhere('thumbnail_path', 'not like', '%.%');
            })
            ->get();

        foreach ($productsToFix as $product) {
            /** @var ?Image $image */
            $image = $product->images()->where('sequence_number', 0)->first(); // Assuming thumbnail is sequence 0
            if ($image && !$imagesToFix->contains('id', $image->id)) {
                $imagesToFix->push($image);
            }
        }

        if ($imagesToFix->isEmpty()) {
            $this->info('No incorrect image paths found.');
            Log::info('No incorrect image paths found.');
            return;
        }

        // $this->info("Found {$imagesToFix->count()} images to fix.");
        Log::info("Found {$imagesToFix->count()} images to fix.");

        $imagesToFix->each(function (Image $image) {
            // Set path to null so the job doesn't skip it
            $image->update(['path' => null]);

            DownloadImage::dispatch($image->imageable_type, $image->imageable_id, $image->original_url, $image->sequence_number);

            // $this->line('Queued re-download for image: ' . $image->id . ' (' . $image->original_url . ')');
            Log::info('Queued re-download for image with incorrect path: ' . $image->id . ' (' . $image->original_url . ')');
        });

        $this->info('Finished queuing jobs to fix image paths.');
    }
}
