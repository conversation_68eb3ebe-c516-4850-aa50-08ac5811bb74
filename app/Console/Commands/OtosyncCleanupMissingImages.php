<?php

namespace App\Console\Commands;

use App\Models\Image;
use App\Models\Product;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class OtosyncCleanupMissingImages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'otosync:cleanup-missing-images {--dry-run}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Scans images and products tables for entries where the file is missing from storage and cleans them up.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');

        if ($isDryRun) {
            $this->info('--- DRY RUN ---');
            $this->info('No database records will be deleted or updated.');
            Log::info('--- DRY RUN ---');
        }

        $this->cleanupImagesTable($isDryRun);
        $this->cleanupProductThumbnails($isDryRun);

        $this->info('Cleanup of missing image records complete.');
        Log::info('Cleanup of missing image records complete.');
    }

    private function cleanupImagesTable(bool $isDryRun): void
    {
        $this->info('Checking images table...');
        Log::info('Checking images table for missing files...');

        foreach (Image::query()->cursor() as $image) {
                $thumbExists = $image->path && Storage::disk('public')->exists($image->path);
                $originalExists = $image->original_image_path && Storage::disk('public')->exists($image->original_image_path);

                if (!$thumbExists && !$originalExists) {
                    // Original logic: If both are missing, delete the record. This is correct.
                    $this->warn("Missing both files for Image ID {$image->id}. Deleting record.");
                    if (!$isDryRun) {
                        $image->delete();
                    }
                } elseif (!$thumbExists && $image->path) {
                    // IMPROVEMENT: If only thumb is missing, nullify its path.
                    $this->warn("Missing thumbnail for Image ID {$image->id}. Clearing path.");
                    if (!$isDryRun) {
                        $image->update(['path' => null]);
                    }
                } elseif (!$originalExists && $image->original_image_path) {
                    // IMPROVEMENT: If only original is missing, nullify its path.
                    $this->warn("Missing original image for Image ID {$image->id}. Clearing path.");
                    if (!$isDryRun) {
                        $image->update(['original_image_path' => null]);
                    }
                }
        }
    }

    private function cleanupProductThumbnails(bool $isDryRun): void
    {
        $this->info('Checking product thumbnails...');
        Log::info('Checking products table for missing thumbnail files...');

        foreach (Product::whereNotNull('thumbnail_path')->cursor() as $product) {
                if (!Storage::disk('public')->exists($product->thumbnail_path)) {
                    Log::warning('Missing thumbnail file found for product.', ['id' => $product->id, 'path' => $product->thumbnail_path]);
                    $this->warn("Missing thumbnail for Product ID {$product->id}: {$product->thumbnail_path}");

                    if (!$isDryRun) {
                        try {
                            $product->update(['thumbnail_path' => null, 'thumbnail_original_url' => null]);
                            $this->info("Cleared thumbnail path for Product ID: {$product->id}");
                            Log::info('Cleared missing thumbnail path from product.', ['id' => $product->id, 'path' => $product->thumbnail_path]);
                        } catch (\Exception $e) {
                            $this->error("Failed to update product record ID {$product->id}: {$e->getMessage()}");
                            Log::error('Failed to update product record.', ['id' => $product->id, 'path' => $product->thumbnail_path, 'error' => $e->getMessage()]);
                        }
                    }
                }
        }
    }
}
