<?php

namespace App\Console\Commands;

use App\Models\SyncLog;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class PruneSyncLogs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'logs:prune-sync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Prune old sync log records from the database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Define how old the logs must be to be deleted.
        // For example, delete logs older than 30 days.
        $retentionDays = 7;
        $cutoffDate = Carbon::now()->subDays($retentionDays);

        $this->info("Pruning sync logs older than {$retentionDays} days (before {$cutoffDate->toDateString()})...");

        // Delete the old records
        $deletedCount = SyncLog::where('created_at', '<', $cutoffDate)->delete();
        Log::info("PruneSyncLogs command executed. Deleted {$deletedCount} records older than {$cutoffDate->toDateString()}.");

        if ($deletedCount > 0) {
            $this->info("Successfully deleted {$deletedCount} old sync log records.");
        } else {
            $this->info('No old sync logs to prune.');
        }

        return 0;
    }
}
