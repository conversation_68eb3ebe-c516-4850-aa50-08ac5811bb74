<?php

namespace App\Console\Commands;

use App\Jobs\DeleteOrphanedStorageFile;
use App\Models\Image;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class OtosyncCleanupUnusedImages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'otosync:cleanup-unused-images {--dry-run}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Scans the storage/app/public/images directory and deletes any files not referenced in the images table.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');

        if ($isDryRun) {
            Log::info('--- DRY RUN ---');
            Log::info('No files will be deleted.');
        }

        Log::info('Fetching all image paths from the database...');
        $dbThumbPaths = Image::whereNotNull('path')->pluck('path')->all();
        $dbOriginalPaths = Image::whereNotNull('original_image_path')->pluck('original_image_path')->all();
        $dbImagePaths = array_merge($dbThumbPaths, $dbOriginalPaths);
        Log::info('Found ' . count($dbImagePaths) . ' total image records in the database.');

        Log::info('Scanning storage directories...');
        $storageThumbPaths = Storage::disk('public')->files('images');
        $storageOriginalPaths = Storage::disk('public')->files('images/originals');
        $storageImagePaths = array_merge($storageThumbPaths, $storageOriginalPaths);
        Log::info('Found ' . count($storageImagePaths) . ' files in storage.');

        $orphanedImages = array_diff($storageImagePaths, $dbImagePaths);

        if (empty($orphanedImages)) {
            Log::info('No orphaned images found. Storage is clean.');
            return;
        }

        Log::warning('Found ' . count($orphanedImages) . ' orphaned images to delete.');

        foreach ($orphanedImages as $path) {
            if ($isDryRun) {
                $this->line("Would dispatch job to delete: {$path}");
            } else {
                DeleteOrphanedStorageFile::dispatch($path);
                $this->line("Dispatched job to delete: {$path}");
            }
        }

        Log::info('Cleanup complete.');
    }
}
