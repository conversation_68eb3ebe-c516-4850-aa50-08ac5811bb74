<?php

namespace App\Console\Commands;

use App\Jobs\StartFullProductSync;
use App\Models\SyncLog;
use Illuminate\Console\Command;

class SyncJubelioProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'otosync:sync-jubelio-products';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch all products from Jubelio and dispatch jobs to sync them.';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Dispatching job to start Jubelio product sync...');
        StartFullProductSync::dispatch(SyncLog::TYPE_SCHEDULED_FULL);
        $this->info('Job dispatched successfully.');

        return self::SUCCESS;
    }
}
