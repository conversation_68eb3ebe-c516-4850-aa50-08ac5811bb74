<?php

namespace App\Jobs;

use App\Models\ProductVariant;
use App\Models\SyncLog;
use App\Services\Jubelio\ProductService;
use App\Services\SyncLoggerService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class ProcessJubelioStockUpdate implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     */
    public int $backoff = 60;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public array $itemIds
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(ProductService $productService, SyncLoggerService $syncLogger): void
    {
        $syncType = SyncLog::TYPE_WEBHOOK_STOCK;
        $logContext = [
            'item_ids' => $this->itemIds,
            'job' => self::class,
        ];

        // disable logging for now
        // $syncLogger->info('Job started', $syncType, $logContext);

        if (empty($this->itemIds)) {
            $syncLogger->warning('No item IDs provided for stock update. Skipping job.', $syncType, $logContext);
            return;
        }

        $stockData = $productService->getProductAllStock($this->itemIds);

        if (!$stockData || empty($stockData['data'])) {
            $syncLogger->info('Failed to fetch stock details from Jubelio for sync.', $syncType, $logContext);
            $this->release(60); // Release back to the queue, try again in 1 minutes
            return;
        }

        try {
            DB::transaction(function () use ($stockData, $syncLogger, $syncType) {
                foreach ($stockData['data'] as $item) {
                    $variant = ProductVariant::where('jubelio_item_id', $item['item_id'])->first();

                    if (!$variant) {
                        $syncLogger->warning('ProductVariant not found for jubelio_item_id: ' . $item['item_id'], $syncType, [
                            'jubelio_item_id' => $item['item_id'],
                        ]);
                        continue;
                    }

                    $beforeUpdate = [
                        'on_hand_qty' => $variant->on_hand_qty,
                        'on_order_qty' => $variant->on_order_qty,
                        'available_qty' => $variant->available_qty,
                    ];

                    $availableQty = max(0, $item['total_stocks']['available'] ?? 0);
                    $updateData = [
                        'on_hand_qty'   => $item['total_stocks']['on_hand'] ?? 0,
                        'on_order_qty'  => $item['total_stocks']['on_order'] ?? 0,
                        'available_qty' => $availableQty,
                    ];
                    $variant->update($updateData);

                    $syncLogger->info('Updated stock for item: ' . $item['item_id'], $syncType, [
                        'reference_id' => $variant->id,
                        'reference_type' => ProductVariant::class,
                        'before' => $beforeUpdate,
                        'after' => $updateData,
                        'original_available_qty' => $item['total_stocks']['available'],
                    ]);
                }
            });
            $syncLogger->info('Job finished successfully', $syncType, $logContext);
        } catch (\Exception $e) {
            $syncLogger->error('Exception during product variant stock update.', $syncType, array_merge($logContext, [
                'exception' => $e->getMessage(),
            ]));
            $this->fail($e);
        }
    }
}
