<?php

namespace App\Jobs;

use App\Models\Image;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class DeleteImageRecordAndFile implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(public Image $image)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // First, delete the file from storage
        if ($this->image->path && Storage::disk('public')->exists($this->image->path)) {
            Storage::disk('public')->delete($this->image->path);
            Log::info('Deleted image file from storage.', ['path' => $this->image->path]);
        }

        // Second, delete the original file from storage if it exists
        if ($this->image->original_image_path && Storage::disk('public')->exists($this->image->original_image_path)) {
            Storage::disk('public')->delete($this->image->original_image_path);
            Log::info('Deleted original image file from storage.', ['path' => $this->image->original_image_path]);
        }

        // third, delete the record from the database
        $this->image->delete();
        Log::info('Deleted image record from database.', ['image_id' => $this->image->id]);
    }
}
