<?php

namespace App\Jobs;

use App\Models\SyncLog;
use App\Services\Jubelio\ProductService;
use App\Services\ProductSyncService;
use App\Services\SyncLoggerService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class SyncJubelioProductVariant implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(public int $jubelioItemId)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(ProductSyncService $productSyncService, ProductService $jubelioProductService, SyncLoggerService $syncLogger): void
    {
        $logContext = [
            'reference_id' => $this->jubelioItemId,
            'reference_type' => 'item_id',
            'job' => self::class,
        ];

        // disable logging for now
        // $syncLogger->info('Job started', SyncLog::TYPE_MANUAL_SINGLE_VARIANT, $logContext);

        $detailedProductData = $jubelioProductService->getProductDetail($this->jubelioItemId);

        if (is_null($detailedProductData)) {
            $syncLogger->warning('Failed to retrieve detailed product data from Jubelio. Releasing job back to queue.', SyncLog::TYPE_MANUAL_SINGLE_VARIANT, $logContext);
            $this->release(60);
            return;
        }

        $syncLogger->info('Detailed product data retrieved from Jubelio.', SyncLog::TYPE_MANUAL_SINGLE_VARIANT, $logContext);

        $productSyncService->syncSingleVariantFromJubelioDetail($detailedProductData, $this->jubelioItemId);

        $syncLogger->info('Product variant sync successful.', SyncLog::TYPE_MANUAL_SINGLE_VARIANT, $logContext);
    }

    /**
     * Handle a job failure.
     */
    public function failed(?Throwable $exception): void
    {
        $syncLogger = app(SyncLoggerService::class);

        $logContext = [
            'reference_id' => $this->jubelioItemId,
            'reference_type' => 'item_id',
            'job' => self::class,
            'exception' => $exception ? $exception->getMessage() : 'Unknown error',
        ];

        $syncLogger->error('Job failed permanently', SyncLog::TYPE_MANUAL_SINGLE_VARIANT, $logContext);
    }
}
