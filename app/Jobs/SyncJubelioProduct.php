<?php

namespace App\Jobs;

use App\Services\Jubelio\ProductService;
use App\Models\SyncLog;
use App\Services\ProductSyncService;
use App\Services\SyncLoggerService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Throwable;

class SyncJubelioProduct implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(public array $productData, public string $syncType)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(ProductSyncService $productSyncService, ProductService $jubelioProductService, SyncLoggerService $syncLogger): void
    {
        $itemGroupId = $this->productData['item_group_id'] ?? null;
        $itemId = $this->productData['variants'][0]['item_id'] ?? null;

        $logContext = [
            'reference_id' => $itemGroupId,
            'reference_type' => 'item_group_id',
            'item_id' => $itemId,
            'job' => self::class,
        ];

        // disable logging for now
        // $syncLogger->info('Job started', $this->syncType, $logContext);

        if ($this->batch() && $this->batch()->cancelled()) {
            $syncLogger->warning('Batch cancelled. Skipping job.', $this->syncType, $logContext);
            return;
        }

        if (!$itemId) {
            $this->fail('Missing item_id in product data.');
            $syncLogger->error('Missing item_id from variants in product data.', $this->syncType, array_merge($logContext, ['product_data' => $this->productData]));
            return;
        }

        $detailedProductData = $jubelioProductService->getProductDetail($itemId);

        if (is_null($detailedProductData)) {
            $syncLogger->warning('Failed to retrieve detailed product data from Jubelio. Releasing job back to queue.', $this->syncType, $logContext);
            $this->release(60);
            return;
        }

        $syncLogger->info('Detailed product data retrieved from Jubelio.', $this->syncType, $logContext);

        $productSyncService->syncProductFromJubelio($this->productData, $detailedProductData);

        $syncLogger->info('Product sync successful.', $this->syncType, $logContext);
    }

    /**
     * Handle a job failure.
     */
    public function failed(?Throwable $exception): void
    {
        $syncLogger = app(SyncLoggerService::class);
        $itemGroupId = $this->productData['item_group_id'] ?? null;
        $itemId = $this->productData['variants'][0]['item_id'] ?? null;

        $logContext = [
            'reference_id' => $itemGroupId,
            'reference_type' => 'item_group_id',
            'item_id' => $itemId,
            'job' => self::class,
            'exception' => $exception ? $exception->getMessage() : 'Unknown error',
        ];

        $syncLogger->error('Job failed permanently', $this->syncType, $logContext);
    }
}
