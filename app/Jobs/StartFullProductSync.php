<?php

namespace App\Jobs;

use App\Models\SyncLog;
use App\Services\Jubelio\ProductService as JubelioProductService;
use Illuminate\Bus\Batch;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Throwable;

class StartFullProductSync implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(public string $syncType = SyncLog::TYPE_SCHEDULED_FULL)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(JubelioProductService $jubelioProductService): void
    {
        $page = 1;

        do {
            $response = $jubelioProductService->getProducts(['page' => $page, 'pageSize' => 50, 'sortDirection' => 'ASC', 'sortBy' => 'last_modified']);
            $products = $response['data'] ?? [];

            if (empty($products)) {
                break;
            }

            $jobs = [];
            foreach ($products as $productData) {
                $jobs[] = new SyncJubelioProduct($productData, $this->syncType);
            }

            if (!empty($jobs)) {
                Bus::batch($jobs)
                    ->name("Jubelio Product Sync - Page {$page} ({$this->syncType})")
                    ->dispatch();
            }

            $page++;
        } while (true);
    }
}
