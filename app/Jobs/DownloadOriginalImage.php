<?php

namespace App\Jobs;

use App\Models\Image;
use App\Models\SyncLog;
use App\Services\SyncLoggerService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class DownloadOriginalImage implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 3;
    public int $backoff = 120;

    /**
     * Create a new job instance.
     */
    public function __construct(public int $imageId, public string $originalImageUrl)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(SyncLoggerService $syncLogger): void
    {
        $image = Image::find($this->imageId);

        if (!$image) {
            // This can happen if the image record was deleted before the job ran.
            return;
        }

        $logContext = [
            'reference_id' => $image->imageable_id,
            'reference_type' => $image->imageable_type,
            'image_url' => $this->originalImageUrl,
            'job' => self::class,
        ];

        $syncType = SyncLog::TYPE_ORIGINAL_IMAGE_DOWNLOAD;



        // Skip if already downloaded
        if ($image->original_image_path && Storage::disk('public')->exists($image->original_image_path)) {
            $syncLogger->info('Original image already exists. Skipping download.', $syncType, $logContext);
            return;
        }

        try {
            $response = Http::timeout(60)->get($this->originalImageUrl);

            if ($response->successful()) {
                $extension = pathinfo(parse_url($this->originalImageUrl, PHP_URL_PATH), PATHINFO_EXTENSION) ?: 'jpg';
                $filename = Str::uuid() . '.' . $extension;
                $path = 'images/originals/' . $filename; // Store in a sub-directory

                Storage::disk('public')->put($path, $response->body());

                $image->update(['original_image_path' => $path]);
                $syncLogger->info('Original image downloaded and processed successfully.', $syncType, array_merge($logContext, ['path' => $path]));
            } else {
                if ($response->status() == 404) {
                    $syncLogger->warning('Original image not found at URL (404). Skipping.', $syncType, array_merge($logContext, ['status' => $response->status()]));
                } else {
                    $syncLogger->error('Failed to download original image. HTTP status: ' . $response->status(), $syncType, array_merge($logContext, ['status' => $response->status()]));
                    $this->fail();
                }
            }
        } catch (\Exception $e) {
            $syncLogger->error('Failed to download original image due to an exception.', $syncType, array_merge($logContext, [
                'exception' => $e->getMessage(),
            ]));
            report($e);
            $this->fail($e);
        }
    }
}
