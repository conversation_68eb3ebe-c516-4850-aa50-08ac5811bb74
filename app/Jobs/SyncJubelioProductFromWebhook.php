<?php

namespace App\Jobs;

use App\Models\SyncLog;
use App\Services\Jubelio\ProductService;
use App\Services\ProductSyncService;
use App\Services\SyncLoggerService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class SyncJubelioProductFromWebhook implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public int $itemGroupId,
        public ?string $thumbnail = null,
        public string $syncType = SyncLog::TYPE_WEBHOOK
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(ProductSyncService $productSyncService, ProductService $jubelioProductService, SyncLoggerService $syncLogger): void
    {
        $logContext = [
            'reference_id' => $this->itemGroupId,
            'reference_type' => 'item_group_id',
            'job' => self::class,
        ];

        try {
            // disable logging for now
            // $syncLogger->info('Job started', $this->syncType, $logContext);

            $detailedProductData = $jubelioProductService->getProductDetailByItemGroupId($this->itemGroupId);

            if (is_null($detailedProductData)) {
                $syncLogger->warning('Failed to retrieve detailed product data from Jubelio. Releasing job back to queue.', $this->syncType, $logContext);
                $this->release(60);
                return;
            }

            $syncLogger->info('Detailed product data retrieved from Jubelio.', $this->syncType, $logContext);

            $productSyncService->syncProductFromJubelioDetail($detailedProductData, $this->thumbnail);

            $syncLogger->info('Product sync from webhook successful.', $this->syncType, $logContext);
        } catch (Throwable $e) {
            // The failed() method will handle logging this.
            $this->fail($e);
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(?Throwable $exception): void
    {
        $syncLogger = app(SyncLoggerService::class);

        $logContext = [
            'reference_id' => $this->itemGroupId,
            'reference_type' => 'item_group_id',
            'job' => self::class,
            'exception' => $exception ? $exception->getMessage() : 'Unknown error',
        ];

        $syncLogger->error('Job failed permanently', $this->syncType, $logContext);
    }
}
