<?php

namespace App\Jobs;

use App\Models\SyncLog;
use App\Services\SyncLoggerService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class DownloadImage implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 3;

    public int $backoff = 120;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public string $imageableType,
        public int $imageableId,
        public string $imageUrl,
        public int $sequence = 0,
        public ?string $originalImageUrl = null
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(SyncLoggerService $syncLogger): void
    {
        $logContext = [
            'reference_id' => $this->imageableId,
            'reference_type' => $this->imageableType,
            'image_url' => $this->imageUrl,
            'job' => self::class,
        ];

        $syncType = $this->imageableType === \App\Models\Product::class
            ? SyncLog::TYPE_THUMBNAIL_IMAGE_DOWNLOAD
            : SyncLog::TYPE_VARIANT_IMAGE_DOWNLOAD;

        // disable logging for now
        // $syncLogger->info('Job started', $syncType, $logContext);

        $model = app($this->imageableType)->find($this->imageableId);

        if (!$model) {
            $syncLogger->warning('Model not found. Skipping image download.', $syncType, $logContext);
            return;
        }

        $image = $model->images()->where(function ($query) {
            $query->where('original_url', $this->imageUrl)
                ->orWhere('original_image_url', $this->originalImageUrl);

            if ($this->originalImageUrl) {
                $query->orWhere('original_url', $this->originalImageUrl);
            }
        })->first();

        if ($image && $image->path) {
            if ($this->imageableType === \App\Models\Product::class && $this->sequence === 0 && !$model->thumbnail_path) {
                $model->update([
                    'thumbnail_path' => $image->path,
                    'thumbnail_original_url' => $image->original_url,
                ]);
            }
            $syncLogger->info('Image already exists. Checking for data backfill.', $syncType, $logContext);

            // --- BACKFILL LOGIC ---
            // If the job was dispatched with the new format (separate thumb/original URLs),
            // but the found record is in the old format, update it.
            if ($this->originalImageUrl && $image->original_url !== $this->imageUrl) {
                $image->update([
                    'original_url' => $this->imageUrl, // Set to thumbnail URL
                    'original_image_url' => $this->originalImageUrl, // Set to original URL
                ]);
                $syncLogger->info('Backfilled image URL structure.', $syncType, $logContext);
            }
            // --- END BACKFILL LOGIC ---

            // Even if thumbnail exists, check if the original image is missing and needs downloading.
            if ($this->originalImageUrl && !$image->original_image_path) {
                DownloadOriginalImage::dispatch($image->id, $this->originalImageUrl);
                $syncLogger->info('Original image is missing, dispatching download job.', $syncType, $logContext);
            }

            return;
        }

        try {
            $response = Http::timeout(60)->get($this->imageUrl);

            if ($response->successful()) {
                $contentType = $response->header('Content-Type');
                $extension = $this->getExtensionFromContentType($contentType) ?? pathinfo(parse_url($this->imageUrl, PHP_URL_PATH), PATHINFO_EXTENSION);

                if (!$extension) {
                    $syncLogger->warning('Could not determine image type for URL.', $syncType, array_merge($logContext, ['content_type' => $contentType]));
                    return;
                }

                $filename = Str::uuid() . '.' . $extension;
                $path = 'images/' . $filename;

                Storage::disk('public')->put($path, $response->body());

                $imageData = [
                    'path' => $path,
                    'original_url' => $this->imageUrl,
                    'original_image_url' => $this->originalImageUrl,
                    'sequence_number' => $this->sequence,
                ];

                if ($image) {
                    $image->update($imageData);
                } else {
                    $image = $model->images()->create($imageData);
                }

                if ($this->imageableType === \App\Models\Product::class && $this->sequence === 0) {
                    $model->update([
                        'thumbnail_path' => $image->path,
                        'thumbnail_original_url' => $image->original_url,
                    ]);
                }

                if ($this->originalImageUrl) {
                    DownloadOriginalImage::dispatch($image->id, $this->originalImageUrl);
                }

                $syncLogger->info('Image downloaded and processed successfully.', $syncType, array_merge($logContext, ['path' => $path]));
            } else {
                if (!$image) {
                    $model->images()->create([
                        'path' => null,
                        'original_url' => $this->imageUrl,
                        'original_image_url' => $this->originalImageUrl,
                        'sequence_number' => $this->sequence,
                    ]);
                }
                $syncLogger->error('Failed to download image. HTTP status: ' . $response->status(), $syncType, array_merge($logContext, ['status' => $response->status()]));
                $this->fail();
            }
        } catch (\Exception $e) {
            if (!$image) {
                $model->images()->create([
                    'path' => null,
                    'original_url' => $this->imageUrl,
                    'original_image_url' => $this->originalImageUrl,
                    'sequence_number' => $this->sequence,
                ]);
            }
            $syncLogger->error('Failed to download image due to an exception.', $syncType, array_merge($logContext, [
                'exception' => $e->getMessage(),
            ]));
            report($e);
            $this->fail($e);
        }
    }

    private function getExtensionFromContentType(?string $contentType): ?string
    {
        if (!$contentType) {
            return null;
        }

        $mimeTypes = [
            'image/jpeg' => 'jpg',
            'image/jpg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'image/webp' => 'webp',
            'image/svg+xml' => 'svg',
        ];

        foreach ($mimeTypes as $mime => $extension) {
            if (str_starts_with($contentType, $mime)) {
                return $extension;
            }
        }

        return null;
    }
}
