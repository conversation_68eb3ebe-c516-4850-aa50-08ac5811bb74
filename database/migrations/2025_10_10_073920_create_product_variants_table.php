<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_variants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->unsignedBigInteger('jubelio_item_id')->index();
            $table->string('sku')->index();
            $table->string('name');
            $table->decimal('sell_price', 15, 2);
            $table->decimal('weight', 8, 2)->nullable();
            $table->string('unit')->default('piece');
            $table->string('barcode')->nullable();
            $table->integer('available_qty')->default(0);
            $table->integer('on_hand_qty')->default(0);
            $table->integer('on_order_qty')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_variants');
    }
};
