<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('jubelio_item_group_id')->index();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('brand_name')->nullable();
            $table->string('thumbnail_path')->nullable();
            $table->string('thumbnail_original_url')->nullable();
            $table->boolean('is_consignment')->default(false);
            $table->decimal('package_height', 8, 2)->nullable();
            $table->decimal('package_width', 8, 2)->nullable();
            $table->decimal('package_length', 8, 2)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
