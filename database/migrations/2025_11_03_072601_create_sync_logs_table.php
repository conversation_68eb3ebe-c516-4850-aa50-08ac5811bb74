<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sync_logs', function (Blueprint $table) {
            $table->id();
            $table->string('level'); // e.g., 'info', 'error', 'warning'
            $table->string('message');
            $table->string('sync_type'); // e.g., 'full_sync', 'webhook', 'single_product', 'image_download'
            $table->unsignedBigInteger('reference_id')->nullable(); // e.g., product_id, product_variant_id, item_group_id
            $table->string('reference_type')->nullable(); // e.g., 'item_group_id', 'item_id'
            $table->json('context')->nullable(); // Store extra data like API responses or error traces
            $table->timestamps();

            $table->index('level');
            $table->index(['reference_id', 'reference_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sync_logs');
    }
};
