<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => '<PERSON><PERSON><PERSON><PERSON><PERSON>',
                'password' => 'Bismillah3x',
                'phone' => '085336383975',
                'role' => User::ROLE_ADMIN,
                'is_active' => true,
            ]
        );

        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Otoapi',
                'password' => 'MJ3dgSKiedhy1KZI',
                'phone' => null,
                'role' => User::ROLE_SYSTEM,
                'is_active' => true,
            ]
        );
    }
}
