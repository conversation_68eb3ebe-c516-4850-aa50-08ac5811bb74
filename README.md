<p align="center"><a href="https://laravel.com" target="_blank"><img src="https://raw.githubusercontent.com/laravel/art/master/logo-lockup/5%20SVG/2%20CMYK/1%20Full%20Color/laravel-logolockup-cmyk-red.svg" width="400" alt="Laravel Logo"></a></p>

<p align="center">
<a href="https://github.com/laravel/framework/actions"><img src="https://github.com/laravel/framework/workflows/tests/badge.svg" alt="Build Status"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/dt/laravel/framework" alt="Total Downloads"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/v/laravel/framework" alt="Latest Stable Version"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/l/laravel/framework" alt="License"></a>
</p>

## About Otosync

Otosync is a Laravel-based application designed to synchronize product data.

## Installation Guide

### Prerequisites

Make sure you have the following software installed on your machine:

*   PHP >= 8.2
*   Composer
*   Node.js & npm
*   A database server (e.g., MySQL, PostgreSQL)

### Step-by-Step Installation

1.  **Clone the repository:**

    ```bash
    git clone <repository-url>
    cd otosync
    ```

2.  **Install PHP dependencies:**

    ```bash
    composer install
    ```

3.  **Install JavaScript dependencies:**

    ```bash
    npm install
    ```

4.  **Create your environment file:**

    Copy the example environment file and generate an application key.

    ```bash
    cp .env.example .env
    php artisan key:generate
    ```

5.  **Configure your environment variables in `.env`:**

    Open the `.env` file and update your database credentials and any other necessary configurations.

    ```ini
    APP_NAME=Otosync
    APP_ENV=local
    APP_KEY=...
    APP_DEBUG=true
    APP_URL=http://localhost

    DB_CONNECTION=mysql
    DB_HOST=127.0.0.1
    DB_PORT=3306
    DB_DATABASE=otosync
    DB_USERNAME=root
    DB_PASSWORD=

    # Jubelio Data
    JUBELIO_BASE_URL=https://api2.jubelio.com
    JUBELIO_EMAIL=
    JUBELIO_PASSWORD=
    JUBELIO_SECRET_KEY=
    IMAGE_BASE_URL="${APP_URL}/storage/"
    ```

6.  **Run database migrations:**

    This will create the necessary tables in your database.

    ```bash
    php artisan migrate
    ```

7.  **Link Storage:**
    ```bash
    php artisan storage:link
    ```

8.  **Run the development server:**

    ```bash
    php artisan serve
    ```

    Your application should now be running at `http://localhost:8000`.

## Contributing

Thank you for considering contributing to the Laravel framework! The contribution guide can be found in the [Laravel documentation](https://laravel.com/docs/contributions).

## Code of Conduct

In order to ensure that the Laravel community is welcoming to all, please review and abide by the [Code of Conduct](https://laravel.com/docs/contributions#code-of-conduct).

## Security Vulnerabilities

If you discover a security vulnerability within Laravel, please send an e-mail to Taylor Otwell via [<EMAIL>](mailto:<EMAIL>). All security vulnerabilities will be promptly addressed.

## License

The Laravel framework is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).


## ide helper
`````
php artisan ide-helper:models -RWM
