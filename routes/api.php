<?php

use Illuminate\Http\Request;
use App\Http\Controllers\Api\AttributeController;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\JubelioWebhookController;
use App\Http\Controllers\Api\ProductController;
use App\Http\Controllers\Api\ProductSyncController;
use App\Http\Controllers\Api\SyncLogController;
use App\Models\User;
use Illuminate\Support\Facades\Route;

// Route::get('/user', function (Request $request) {
//     return $request->user();
// })->middleware('auth:sanctum');

Route::get('/', function (Request $request) {
    return response()->json([
        'message' => 'Wellcome to Otosync API.'
    ]);
});


Route::prefix('webhooks/jubelio')->group(function () {
    Route::post('product', [JubelioWebhookController::class, 'handleProduct'])->name('webhook.jubelio.product');
    Route::post('stock', [JubelioWebhookController::class, 'handleStock'])->name('webhook.jubelio.stock');
    Route::post('price', [JubelioWebhookController::class, 'handlePrice'])->name('webhook.jubelio.price');
    Route::post('stock-transfer', [JubelioWebhookController::class, 'handleStockTransfer'])->name('webhook.jubelio.stock-transfer');
});

Route::middleware('check-app-identifier')->group(function () {
    Route::post('/login', [AuthController::class, 'login']);

    Route::apiResource('products', ProductController::class)->only(['index', 'show']);
    Route::get('attributes', [AttributeController::class, 'index']);

    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::middleware('role:' . User::ROLE_ADMIN . ',' . User::ROLE_SYSTEM)->group(function () {
            Route::post('/products/sync-all', [ProductSyncController::class, 'syncAll']);
            Route::post('/products/sync-single', [ProductSyncController::class, 'syncSingle']);
            Route::post('/products/check-sync-batch', [ProductSyncController::class, 'checkSyncStatusBatch']);
            Route::post('/products/variants/sync-single', [ProductSyncController::class, 'syncSingleVariant']);
            Route::post('/products/variants/check-sync-batch', [ProductSyncController::class, 'checkVariantSyncStatusBatch']);

            Route::apiResource('sync-logs', SyncLogController::class)->only(['index', 'show']);
        });
    });

});





