<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

Schedule::command('sanctum:prune-expired --hours=24')->daily();
Schedule::command('otosync:sync-jubelio-products')->dailyAt('00:01');
Schedule::command('otosync:redownload-missing-images')->dailyAt('03:00');
Schedule::command('otosync:cleanup-unused-images')->quarterlyOn(1, '04:00');
// Schedule::command('otosync:cleanup-missing-images')->weeklyOn(0, '05:00');
Schedule::command('otosync:cleanup-missing-images')->monthlyOn(1, '05:00');
Schedule::command('logs:prune-sync')->dailyAt('00:30');
